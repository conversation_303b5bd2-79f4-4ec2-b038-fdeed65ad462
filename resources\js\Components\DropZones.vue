<script setup>
import { ref, computed } from 'vue'
import draggable from 'vuedraggable'
import { useTodoDragAndDrop } from '@/composables/useDragAndDrop'

const props = defineProps({
    categories: Array,
    showDropZones: {
        type: Boolean,
        default: false
    }
})

const {
    isDragging,
    draggedItem,
    handlePriorityChange,
    handleStatusChange,
    handleCategoryChange,
    canDrop,
    getDragOptions
} = useTodoDragAndDrop()

// Priority zones
const priorityZones = ref({
    high: [],
    medium: [],
    low: []
})

// Status zones
const statusZones = ref({
    pending: [],
    completed: [],
    cancelled: []
})

// Category zones
const categoryZones = ref({})

// Initialize category zones
props.categories.forEach(category => {
    categoryZones.value[category.id] = []
})

// Handle priority drop
const onPriorityDrop = (priority) => {
    if (draggedItem.value && canDrop('priority', priority, draggedItem.value)) {
        handlePriorityChange(draggedItem.value, priority)
        priorityZones.value[priority] = []
    }
}

// Handle status drop
const onStatusDrop = (status) => {
    if (draggedItem.value && canDrop('status', status, draggedItem.value)) {
        handleStatusChange(draggedItem.value, status)
        statusZones.value[status] = []
    }
}

// Handle category drop
const onCategoryDrop = (categoryId) => {
    if (draggedItem.value && canDrop('category', categoryId, draggedItem.value)) {
        handleCategoryChange(draggedItem.value, categoryId)
        categoryZones.value[categoryId] = []
    }
}

// Get priority color classes
const getPriorityClasses = (priority) => {
    const base = 'border-2 border-dashed rounded-lg p-4 text-center transition-all duration-200'
    switch (priority) {
        case 'high':
            return `${base} border-red-300 bg-red-50 text-red-700 hover:border-red-400 hover:bg-red-100`
        case 'medium':
            return `${base} border-yellow-300 bg-yellow-50 text-yellow-700 hover:border-yellow-400 hover:bg-yellow-100`
        case 'low':
            return `${base} border-green-300 bg-green-50 text-green-700 hover:border-green-400 hover:bg-green-100`
        default:
            return `${base} border-gray-300 bg-gray-50 text-gray-700`
    }
}

// Get status color classes
const getStatusClasses = (status) => {
    const base = 'border-2 border-dashed rounded-lg p-4 text-center transition-all duration-200'
    switch (status) {
        case 'pending':
            return `${base} border-blue-300 bg-blue-50 text-blue-700 hover:border-blue-400 hover:bg-blue-100`
        case 'completed':
            return `${base} border-green-300 bg-green-50 text-green-700 hover:border-green-400 hover:bg-green-100`
        case 'cancelled':
            return `${base} border-gray-300 bg-gray-50 text-gray-700 hover:border-gray-400 hover:bg-gray-100`
        default:
            return `${base} border-gray-300 bg-gray-50 text-gray-700`
    }
}

// Get category color classes
const getCategoryClasses = (category) => {
    const base = 'border-2 border-dashed rounded-lg p-4 text-center transition-all duration-200'
    return `${base} border-purple-300 bg-purple-50 text-purple-700 hover:border-purple-400 hover:bg-purple-100`
}
</script>

<template>
    <div v-if="showDropZones && isDragging" class="fixed inset-0 z-50 bg-black bg-opacity-20 flex items-center justify-center p-8">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto">
            <div class="text-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Drop to Change</h3>
                <p class="text-sm text-gray-600 mt-1">Drag the todo to any zone below to update its properties</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Priority Drop Zones -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Priority</h4>
                    <div class="space-y-3">
                        <draggable
                            v-model="priorityZones.high"
                            v-bind="getDragOptions('priority-high')"
                            @change="onPriorityDrop('high')"
                            :class="getPriorityClasses('high')"
                            :disabled="!canDrop('priority', 'high', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span class="font-medium">High Priority</span>
                                </div>
                            </template>
                        </draggable>

                        <draggable
                            v-model="priorityZones.medium"
                            v-bind="getDragOptions('priority-medium')"
                            @change="onPriorityDrop('medium')"
                            :class="getPriorityClasses('medium')"
                            :disabled="!canDrop('priority', 'medium', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <span class="font-medium">Medium Priority</span>
                                </div>
                            </template>
                        </draggable>

                        <draggable
                            v-model="priorityZones.low"
                            v-bind="getDragOptions('priority-low')"
                            @change="onPriorityDrop('low')"
                            :class="getPriorityClasses('low')"
                            :disabled="!canDrop('priority', 'low', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="font-medium">Low Priority</span>
                                </div>
                            </template>
                        </draggable>
                    </div>
                </div>

                <!-- Status Drop Zones -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Status</h4>
                    <div class="space-y-3">
                        <draggable
                            v-model="statusZones.pending"
                            v-bind="getDragOptions('status-pending')"
                            @change="onStatusDrop('pending')"
                            :class="getStatusClasses('pending')"
                            :disabled="!canDrop('status', 'pending', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <span class="font-medium">Pending</span>
                                </div>
                            </template>
                        </draggable>

                        <draggable
                            v-model="statusZones.completed"
                            v-bind="getDragOptions('status-completed')"
                            @change="onStatusDrop('completed')"
                            :class="getStatusClasses('completed')"
                            :disabled="!canDrop('status', 'completed', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="font-medium">Completed</span>
                                </div>
                            </template>
                        </draggable>

                        <draggable
                            v-model="statusZones.cancelled"
                            v-bind="getDragOptions('status-cancelled')"
                            @change="onStatusDrop('cancelled')"
                            :class="getStatusClasses('cancelled')"
                            :disabled="!canDrop('status', 'cancelled', draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                                    <span class="font-medium">Cancelled</span>
                                </div>
                            </template>
                        </draggable>
                    </div>
                </div>

                <!-- Category Drop Zones -->
                <div>
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Categories</h4>
                    <div class="space-y-3 max-h-64 overflow-y-auto">
                        <draggable
                            v-for="category in categories"
                            :key="category.id"
                            v-model="categoryZones[category.id]"
                            v-bind="getDragOptions(`category-${category.id}`)"
                            @change="onCategoryDrop(category.id)"
                            :class="getCategoryClasses(category)"
                            :disabled="!canDrop('category', category.id, draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: category.color }"></div>
                                    <span class="font-medium">{{ category.name }}</span>
                                </div>
                            </template>
                        </draggable>

                        <!-- No Category Zone -->
                        <draggable
                            v-model="categoryZones['null']"
                            v-bind="getDragOptions('category-null')"
                            @change="onCategoryDrop(null)"
                            :class="getCategoryClasses(null)"
                            :disabled="!canDrop('category', null, draggedItem)"
                        >
                            <template #header>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                    <span class="font-medium">No Category</span>
                                </div>
                            </template>
                        </draggable>
                    </div>
                </div>
            </div>

            <div class="mt-6 text-center">
                <p class="text-xs text-gray-500">Release the mouse button over any zone to apply the change</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.drag-over {
    border-color: #3b82f6 !important;
    background-color: #dbeafe !important;
    transform: scale(1.02);
}
</style>
