<script setup>
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import FilterBar from '@/Components/FilterBar.vue';
import Pagination from '@/Components/Pagination.vue';
import BulkActions from '@/Components/BulkActions.vue';
import DraggableTodoList from '@/Components/DraggableTodoList.vue';
import DropZones from '@/Components/DropZones.vue';
import KanbanView from '@/Components/KanbanView.vue';
import RealTimeNotification from '@/Components/RealTimeNotification.vue';
import ConnectionStatus from '@/Components/ConnectionStatus.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { ref, computed } from 'vue';
import { useBulkOperationShortcuts } from '@/composables/useKeyboardShortcuts';
import { useTodoRealTimeUpdates } from '@/composables/useRealTimeUpdates';

const props = defineProps({
    todos: Object,
    categories: Array,
    filters: Object,
});

const selectedTodos = ref([]);
const enableDragAndDrop = ref(true);
const showDropZones = ref(false);
const viewMode = ref('list'); // 'list' or 'kanban'

// Real-time updates
const {
    isConnected,
    lastUpdate,
    showNotification,
    notificationMessage,
    showUpdateNotification,
    refreshPageData
} = useTodoRealTimeUpdates();

const allSelected = computed(() => {
    return props.todos.data.length > 0 && selectedTodos.value.length === props.todos.data.length;
});

const someSelected = computed(() => {
    return selectedTodos.value.length > 0 && selectedTodos.value.length < props.todos.data.length;
});

const toggleSelectAll = () => {
    if (allSelected.value) {
        selectedTodos.value = [];
    } else {
        selectedTodos.value = props.todos.data.map(todo => todo.id);
    }
};

const toggleSelectTodo = (todoId) => {
    const index = selectedTodos.value.indexOf(todoId);
    if (index > -1) {
        selectedTodos.value.splice(index, 1);
    } else {
        selectedTodos.value.push(todoId);
    }
};

const toggleDragAndDrop = () => {
    enableDragAndDrop.value = !enableDragAndDrop.value;
};

const toggleViewMode = () => {
    viewMode.value = viewMode.value === 'list' ? 'kanban' : 'list';
};

const clearSelection = () => {
    selectedTodos.value = [];
};

const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
};

const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high': return 'text-red-600 bg-red-50 border-red-200';
        case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        case 'low': return 'text-green-600 bg-green-50 border-green-200';
        default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
};

const getStatusColor = (status) => {
    switch (status) {
        case 'completed': return 'text-green-600 bg-green-50';
        case 'pending': return 'text-blue-600 bg-blue-50';
        case 'cancelled': return 'text-gray-600 bg-gray-50';
        default: return 'text-gray-600 bg-gray-50';
    }
};

const toggleStatus = (todo) => {
    router.patch(route('todos.toggle-status', todo.id), {}, {
        preserveScroll: true,
        onSuccess: () => {
            // Refresh the page data
            router.reload({ only: ['todos'] });
        }
    });
};

const deleteTodo = (todo) => {
    if (confirm('Are you sure you want to delete this todo?')) {
        router.delete(route('todos.destroy', todo.id));
    }
};

// Bulk operation functions for shortcuts
const markSelectedComplete = () => {
    if (selectedTodos.value.length === 0) return;

    router.post(route('todos.bulk-update'), {
        todo_ids: selectedTodos.value,
        action: 'complete'
    }, {
        onSuccess: () => {
            selectedTodos.value = [];
        }
    });
};

const deleteSelected = () => {
    if (selectedTodos.value.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedTodos.value.length} selected todos?`)) {
        router.post(route('todos.bulk-update'), {
            todo_ids: selectedTodos.value,
            action: 'delete'
        }, {
            onSuccess: () => {
                selectedTodos.value = [];
            }
        });
    }
};

// Setup keyboard shortcuts
useBulkOperationShortcuts({
    selectAll: toggleSelectAll,
    clearSelection: clearSelection,
    markComplete: markSelectedComplete,
    deleteSelected: deleteSelected,
});
</script>

<template>
    <Head title="All Todos" />

    <AuthenticatedLayout>
        <template #header>
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h2 class="text-xl font-semibold leading-tight text-foreground">
                        All Todos
                    </h2>
                    <!-- Real-time connection status -->
                    <ConnectionStatus
                        :is-connected="isConnected"
                        :last-update="lastUpdate"
                        :show-details="true"
                    />
                </div>
                <div class="flex space-x-3">
                    <!-- View Mode Toggle -->
                    <button
                        @click="toggleViewMode"
                        :class="[
                            'px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors',
                            viewMode === 'kanban'
                                ? 'bg-purple-600 hover:bg-purple-700 text-white'
                                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                        ]"
                        :title="viewMode === 'kanban' ? 'Switch to List View' : 'Switch to Kanban View'"
                    >
                        <svg v-if="viewMode === 'list'" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0V17m0-10a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2h-2a2 2 0 01-2-2"></path>
                        </svg>
                        <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                        <span>{{ viewMode === 'kanban' ? 'Kanban' : 'List' }}</span>
                    </button>

                    <!-- Drag and Drop Toggle (only show in list view) -->
                    <button
                        v-if="viewMode === 'list'"
                        @click="toggleDragAndDrop"
                        :class="[
                            'px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors',
                            enableDragAndDrop
                                ? 'bg-green-600 hover:bg-green-700 text-white'
                                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                        ]"
                        :title="enableDragAndDrop ? 'Disable Drag & Drop' : 'Enable Drag & Drop'"
                    >
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M7 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"></path>
                        </svg>
                        <span>{{ enableDragAndDrop ? 'Drag ON' : 'Drag OFF' }}</span>
                    </button>

                    <Link :href="route('todos.create')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <span>New Todo</span>
                    </Link>
                    <Link :href="route('dashboard')" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                        </svg>
                        <span>Dashboard</span>
                    </Link>
                </div>
            </div>
        </template>

        <div class="py-6">
            <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
                <!-- Filter Bar -->
                <FilterBar
                    :filters="filters"
                    :categories="categories"
                    :show-category-filter="true"
                    :show-status-filter="true"
                    :show-priority-filter="true"
                />

                <div class="bg-card rounded-lg shadow-sm border border-border">
                    <div class="p-6">
                        <div v-if="todos.data.length === 0" class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-foreground">No todos</h3>
                            <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new todo.</p>
                            <div class="mt-6">
                                <Link :href="route('todos.create')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center space-x-2 transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    <span>New Todo</span>
                                </Link>
                            </div>
                        </div>

                        <div v-else>
                            <!-- List View -->
                            <div v-if="viewMode === 'list'">
                                <!-- Bulk Selection Header -->
                                <div v-if="todos.data.length > 0" class="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border mb-4">
                                    <div class="flex items-center space-x-3">
                                        <label class="flex items-center space-x-2 cursor-pointer">
                                            <input
                                                type="checkbox"
                                                :checked="allSelected"
                                                :indeterminate="someSelected"
                                                @change="toggleSelectAll"
                                                class="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                                            />
                                            <span class="text-sm font-medium text-foreground">
                                                Select All ({{ todos.data.length }} todos)
                                            </span>
                                        </label>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div v-if="selectedTodos.length > 0" class="text-sm text-muted-foreground">
                                            {{ selectedTodos.length }} selected
                                        </div>
                                        <div v-if="enableDragAndDrop" class="text-xs text-muted-foreground bg-green-100 px-2 py-1 rounded">
                                            Drag & Drop Enabled
                                        </div>
                                    </div>
                                </div>

                                <!-- Draggable Todo List -->
                                <DraggableTodoList
                                    :todos="todos.data"
                                    :categories="categories"
                                    :selected-todos="selectedTodos"
                                    :enable-drag-and-drop="enableDragAndDrop"
                                    @toggle-select="toggleSelectTodo"
                                    @toggle-status="toggleStatus"
                                    @delete-todo="deleteTodo"
                                />
                            </div>

                            <!-- Kanban View -->
                            <div v-else-if="viewMode === 'kanban'">
                                <!-- Kanban Header -->
                                <div v-if="todos.data.length > 0" class="flex items-center justify-between p-4 bg-muted/20 rounded-lg border border-border mb-6">
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm font-medium text-foreground">
                                            Kanban Board - {{ todos.data.length }} todos
                                        </span>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div v-if="selectedTodos.length > 0" class="text-sm text-muted-foreground">
                                            {{ selectedTodos.length }} selected
                                        </div>
                                        <div class="text-xs text-muted-foreground bg-purple-100 px-2 py-1 rounded">
                                            Drag & Drop Enabled
                                        </div>
                                    </div>
                                </div>

                                <!-- Kanban Board -->
                                <KanbanView
                                    :todos="todos.data"
                                    :categories="categories"
                                    :selected-todos="selectedTodos"
                                    @toggle-select="toggleSelectTodo"
                                    @toggle-status="toggleStatus"
                                    @delete-todo="deleteTodo"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <Pagination :links="todos.links" :meta="todos.meta" />
                </div>
            </div>
        </div>

        <!-- Drop Zones for Drag and Drop -->
        <DropZones
            :categories="categories"
            :show-drop-zones="showDropZones"
        />

        <!-- Bulk Actions Component -->
        <BulkActions
            :selected-items="selectedTodos"
            :categories="categories"
            type="todos"
            @clear-selection="clearSelection"
        />

        <!-- Real-time Notification -->
        <RealTimeNotification
            :show="showNotification"
            :message="notificationMessage"
            type="info"
            position="top-right"
            @close="showNotification = false"
        />
    </AuthenticatedLayout>
</template>
