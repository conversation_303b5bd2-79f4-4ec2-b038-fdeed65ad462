import { ref, onMounted, onUnmounted } from 'vue'
import { router, usePage } from '@inertiajs/vue3'

export function useRealTimeUpdates() {
    const isConnected = ref(false)
    const lastUpdate = ref(null)
    const updateQueue = ref([])
    const showNotification = ref(false)
    const notificationMessage = ref('')
    
    const page = usePage()
    const userId = page.props.auth?.user?.id

    // Connection status
    const setConnectionStatus = (status) => {
        isConnected.value = status
    }

    // Show notification for real-time updates
    const showUpdateNotification = (message, type = 'info') => {
        notificationMessage.value = message
        showNotification.value = true
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            showNotification.value = false
        }, 3000)
    }

    // Handle todo created event
    const handleTodoCreated = (data) => {
        console.log('Todo created:', data)
        lastUpdate.value = data.timestamp
        
        // Only show notification if it's not the current user's action
        if (data.todo.user_id !== userId) {
            showUpdateNotification(`New todo created: ${data.todo.title}`, 'success')
        }
        
        // Add to update queue for potential refresh
        updateQueue.value.push({
            type: 'created',
            data: data,
            timestamp: Date.now()
        })
        
        // Optionally refresh the page data
        refreshPageData()
    }

    // Handle todo updated event
    const handleTodoUpdated = (data) => {
        console.log('Todo updated:', data)
        lastUpdate.value = data.timestamp
        
        // Only show notification if it's not the current user's action
        if (data.todo.user_id !== userId) {
            const actionText = data.action === 'status_toggled' ? 'status changed' : 'updated'
            showUpdateNotification(`Todo ${actionText}: ${data.todo.title}`, 'info')
        }
        
        // Add to update queue
        updateQueue.value.push({
            type: 'updated',
            data: data,
            timestamp: Date.now()
        })
        
        // Refresh page data
        refreshPageData()
    }

    // Handle todo deleted event
    const handleTodoDeleted = (data) => {
        console.log('Todo deleted:', data)
        lastUpdate.value = data.timestamp
        
        // Only show notification if it's not the current user's action
        if (data.user_id !== userId) {
            showUpdateNotification(`Todo deleted (ID: ${data.todo_id})`, 'warning')
        }
        
        // Add to update queue
        updateQueue.value.push({
            type: 'deleted',
            data: data,
            timestamp: Date.now()
        })
        
        // Refresh page data
        refreshPageData()
    }

    // Refresh page data without full page reload
    const refreshPageData = () => {
        // Use Inertia's reload to refresh data
        router.reload({ 
            only: ['todos', 'categories'],
            preserveScroll: true,
            preserveState: true
        })
    }

    // Clear update queue
    const clearUpdateQueue = () => {
        updateQueue.value = []
    }

    // Get pending updates count
    const getPendingUpdatesCount = () => {
        return updateQueue.value.length
    }

    // Setup Echo listeners
    const setupEchoListeners = () => {
        if (!window.Echo || !userId) {
            console.warn('Echo not available or user not authenticated')
            return
        }

        try {
            // Listen to user-specific channel
            window.Echo.private(`user.${userId}`)
                .listen('.todo.created', handleTodoCreated)
                .listen('.todo.updated', handleTodoUpdated)
                .listen('.todo.deleted', handleTodoDeleted)

            // Listen to general todos channel (optional)
            window.Echo.channel('todos')
                .listen('.todo.created', handleTodoCreated)
                .listen('.todo.updated', handleTodoUpdated)
                .listen('.todo.deleted', handleTodoDeleted)

            // Connection events
            window.Echo.connector.pusher.connection.bind('connected', () => {
                console.log('WebSocket connected')
                setConnectionStatus(true)
                showUpdateNotification('Real-time updates connected', 'success')
            })

            window.Echo.connector.pusher.connection.bind('disconnected', () => {
                console.log('WebSocket disconnected')
                setConnectionStatus(false)
                showUpdateNotification('Real-time updates disconnected', 'warning')
            })

            window.Echo.connector.pusher.connection.bind('error', (error) => {
                console.error('WebSocket error:', error)
                setConnectionStatus(false)
                showUpdateNotification('Real-time connection error', 'error')
            })

        } catch (error) {
            console.error('Error setting up Echo listeners:', error)
        }
    }

    // Cleanup Echo listeners
    const cleanupEchoListeners = () => {
        if (!window.Echo || !userId) return

        try {
            window.Echo.leave(`user.${userId}`)
            window.Echo.leave('todos')
        } catch (error) {
            console.error('Error cleaning up Echo listeners:', error)
        }
    }

    // Initialize on mount
    onMounted(() => {
        setupEchoListeners()
    })

    // Cleanup on unmount
    onUnmounted(() => {
        cleanupEchoListeners()
    })

    return {
        isConnected,
        lastUpdate,
        updateQueue,
        showNotification,
        notificationMessage,
        showUpdateNotification,
        refreshPageData,
        clearUpdateQueue,
        getPendingUpdatesCount,
        setupEchoListeners,
        cleanupEchoListeners
    }
}

// Composable for todo-specific real-time updates
export function useTodoRealTimeUpdates() {
    const {
        isConnected,
        lastUpdate,
        showNotification,
        notificationMessage,
        showUpdateNotification,
        refreshPageData
    } = useRealTimeUpdates()

    return {
        isConnected,
        lastUpdate,
        showNotification,
        notificationMessage,
        showUpdateNotification,
        refreshPageData
    }
}
