<script setup>
import { ref, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import draggable from 'vuedraggable'
import { useTodoDragAndDrop } from '@/composables/useDragAndDrop'

const props = defineProps({
    todos: Array,
    categories: Array,
    selectedTodos: Array,
    enableDragAndDrop: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['toggle-select', 'toggle-status', 'delete-todo'])

const {
    isDragging,
    draggedItem,
    handleTodoReorder,
    handlePriorityChange,
    handleStatusChange,
    handleCategoryChange,
    onDragStart,
    onDragEnd,
    getDragOptions
} = useTodoDragAndDrop()

// Local copy of todos for drag and drop
const localTodos = ref([...props.todos])

// Status drop zones
const statusDropZones = ref({
    pending: [],
    completed: [],
    archived: []
})

// Watch for changes in props.todos and update local copy
watch(() => props.todos, (newTodos) => {
    localTodos.value = [...newTodos]
}, { deep: true })

// Handle drag and drop reordering
const onTodoReorder = (evt) => {
    if (props.enableDragAndDrop && evt.moved) {
        handleTodoReorder(localTodos.value)
    }
}

// Handle drag start
const onTodoDragStart = (evt) => {
    // Find the todo being dragged
    const draggedTodo = localTodos.value[evt.oldIndex]
    if (draggedTodo) {
        onDragStart(draggedTodo)
    }
}

// Handle drag end
const onTodoDragEnd = () => {
    onDragEnd()
    // Clear all drop zones
    statusDropZones.value.pending = []
    statusDropZones.value.completed = []
    statusDropZones.value.archived = []
}

// Handle status change via drop zone
const onStatusDropZoneChange = (evt, status) => {
    if (evt.added) {
        // A todo was added to this drop zone
        const droppedTodo = evt.added.element
        if (droppedTodo && droppedTodo.status !== status) {
            handleStatusChange(droppedTodo, status)
        }
        // Clear the drop zone after handling
        setTimeout(() => {
            statusDropZones.value[status] = []
        }, 100)
    }
}

// Priority colors
const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high': return 'text-red-600 bg-red-50 border-red-200';
        case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        case 'low': return 'text-green-600 bg-green-50 border-green-200';
        default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
};

// Status colors
const getStatusColor = (status) => {
    switch (status) {
        case 'completed': return 'text-green-600 bg-green-50';
        case 'pending': return 'text-blue-600 bg-blue-50';
        case 'archived': return 'text-gray-600 bg-gray-50';
        default: return 'text-gray-600 bg-gray-50';
    }
};

// Format date
const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

// Handle priority drop
const onPriorityDrop = (todo, newPriority) => {
    handlePriorityChange(todo, newPriority)
}

// Handle status drop
const onStatusDrop = (todo, newStatus) => {
    handleStatusChange(todo, newStatus)
}

// Handle category drop
const onCategoryDrop = (todo, newCategoryId) => {
    handleCategoryChange(todo, newCategoryId)
}
</script>

<template>
    <div class="space-y-4">
        <!-- Status Drop Zones (only show when dragging in list view) -->
        <div v-if="isDragging && enableDragAndDrop && draggedItem" class="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <!-- Debug info -->
            <!-- <div class="col-span-3 text-xs text-gray-500 mb-2">
                Dragging: {{ isDragging }}, Item: {{ draggedItem?.title }}
            </div> -->
            <div class="text-center text-sm text-gray-600 mb-2 col-span-3">
                <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
                Drop todo here to change status
            </div>

            <!-- Pending Drop Zone -->
            <draggable
                v-model="statusDropZones.pending"
                v-bind="getDragOptions('status-pending', 'list')"
                @change="(evt) => onStatusDropZoneChange(evt, 'pending')"
                item-key="id"
                class="status-drop-zone min-h-20 p-4 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex flex-col items-center justify-center hover:bg-blue-100 transition-colors"
            >
                <template #item="{ element }">
                    <div class="hidden"></div>
                </template>
                <div class="text-blue-600 text-center">
                    <svg class="w-6 h-6 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-xs font-medium">Pending</div>
                </div>
            </draggable>

            <!-- Completed Drop Zone -->
            <draggable
                v-model="statusDropZones.completed"
                v-bind="getDragOptions('status-completed', 'list')"
                @change="(evt) => onStatusDropZoneChange(evt, 'completed')"
                item-key="id"
                class="status-drop-zone min-h-20 p-4 bg-green-50 border-2 border-dashed border-green-300 rounded-lg flex flex-col items-center justify-center hover:bg-green-100 transition-colors"
            >
                <template #item="{ element }">
                    <div class="hidden"></div>
                </template>
                <div class="text-green-600 text-center">
                    <svg class="w-6 h-6 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-xs font-medium">Completed</div>
                </div>
            </draggable>

            <!-- Archived Drop Zone -->
            <draggable
                v-model="statusDropZones.archived"
                v-bind="getDragOptions('status-archived', 'list')"
                @change="(evt) => onStatusDropZoneChange(evt, 'archived')"
                item-key="id"
                class="status-drop-zone min-h-20 p-4 bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center hover:bg-gray-100 transition-colors"
            >
                <template #item="{ element }">
                    <div class="hidden"></div>
                </template>
                <div class="text-gray-600 text-center">
                    <svg class="w-6 h-6 mx-auto mb-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-xs font-medium">Archived</div>
                </div>
            </draggable>
        </div>

        <!-- Main Todo List -->
        <draggable
            v-model="localTodos"
            v-bind="getDragOptions('todos', 'list')"
            @start="onTodoDragStart"
            @end="onTodoDragEnd"
            @change="onTodoReorder"
            :disabled="!enableDragAndDrop"
            item-key="id"
            class="space-y-4"
        >
            <template #item="{ element: todo }">
                <div 
                    class="bg-muted/30 rounded-lg border border-border p-6 hover:bg-muted/50 transition-colors"
                    :class="{ 
                        'opacity-50': isDragging && todo.id === draggedItem?.id,
                        'cursor-move': enableDragAndDrop,
                        'shadow-lg': isDragging && todo.id === draggedItem?.id
                    }"
                >
                    <div class="flex items-start justify-between">
                        <div class="flex items-center space-x-3 flex-1">
                            <!-- Selection Checkbox -->
                            <label class="flex-shrink-0 cursor-pointer">
                                <input
                                    type="checkbox"
                                    :value="todo.id"
                                    :checked="selectedTodos.includes(todo.id)"
                                    @change="emit('toggle-select', todo.id)"
                                    class="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                                />
                            </label>
                            
                            <!-- Drag Handle (only show when drag is enabled) -->
                            <div v-if="enableDragAndDrop" class="flex-shrink-0 cursor-move text-muted-foreground hover:text-foreground">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M7 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"></path>
                                </svg>
                            </div>
                            
                            <!-- Status Toggle -->
                            <button @click="emit('toggle-status', todo)" class="flex-shrink-0">
                                <div v-if="todo.status === 'completed'" class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div v-else class="w-5 h-5 border-2 border-gray-300 rounded-full hover:border-gray-400 transition-colors"></div>
                            </button>

                            <div class="flex-1">
                                <h3 :class="['text-lg font-semibold', todo.status === 'completed' ? 'line-through text-muted-foreground' : 'text-foreground']">
                                    {{ todo.title }}
                                </h3>
                                <p v-if="todo.description" :class="['text-sm mt-1', todo.status === 'completed' ? 'line-through text-muted-foreground' : 'text-muted-foreground']">
                                    {{ todo.description }}
                                </p>
                                
                                <div class="flex items-center mt-3 space-x-3">
                                    <span :class="getPriorityColor(todo.priority)" class="px-2 py-1 text-xs font-medium rounded-full border">
                                        {{ todo.priority }}
                                    </span>
                                    <span :class="getStatusColor(todo.status)" class="px-2 py-1 text-xs font-medium rounded-full">
                                        {{ todo.status }}
                                    </span>
                                    <span v-if="todo.category" class="px-2 py-1 text-xs font-medium rounded-full bg-accent/50 text-accent-foreground border border-accent">
                                        {{ todo.category.name }}
                                    </span>
                                    <span v-if="todo.due_date" class="text-xs text-muted-foreground">
                                        Due: {{ formatDate(todo.due_date) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-2 ml-4">
                            <Link :href="route('todos.show', todo.id)" class="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </Link>
                            <Link :href="route('todos.edit', todo.id)" class="text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </Link>
                            <button @click="emit('delete-todo', todo)" class="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </draggable>
    </div>
</template>

<style scoped>
.drag-ghost {
    opacity: 0.5;
    background: #c8ebfb !important;
    border: 2px dashed #3b82f6 !important;
    transform: rotate(2deg);
}

.drag-chosen {
    background: #f0f9ff !important;
    border: 2px dashed #3b82f6 !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.drag-active {
    background: #dbeafe !important;
    transform: rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.drag-fallback {
    background: #f9fafb !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

/* Drop zone hover states */
.status-drop-zone:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.status-drop-zone.drag-over {
    background: rgba(59, 130, 246, 0.1) !important;
    border-color: #3b82f6 !important;
    border-width: 3px !important;
}
</style>
