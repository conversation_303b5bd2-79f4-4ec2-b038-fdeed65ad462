import { ref, nextTick } from 'vue'
import { router } from '@inertiajs/vue3'

export function useTodoDragAndDrop() {
    const isDragging = ref(false)
    const draggedItem = ref(null)
    const dropZones = ref({
        priority: {
            high: [],
            medium: [],
            low: []
        },
        status: {
            pending: [],
            completed: [],
            archived: []
        },
        categories: {}
    })

    // Handle todo reordering within the same list
    const handleTodoReorder = (newOrder, preserveScroll = true) => {
        const todoIds = newOrder.map(todo => todo.id)
        
        router.post(route('todos.reorder'), {
            todo_ids: todoIds
        }, {
            preserveScroll,
            onSuccess: () => {
                // Optionally refresh data
            },
            onError: (errors) => {
                console.error('Reorder failed:', errors)
            }
        })
    }

    // Handle priority change via drag and drop
    const handlePriorityChange = (todo, newPriority) => {
        if (todo.priority === newPriority) return

        router.patch(route('todos.update-priority', todo.id), {
            priority: newPriority
        }, {
            preserveScroll: true,
            onSuccess: () => {
                // Update local state if needed
            },
            onError: (errors) => {
                console.error('Priority change failed:', errors)
            }
        })
    }

    // Handle status change via drag and drop
    const handleStatusChange = (todo, newStatus) => {
        if (todo.status === newStatus) return

        router.patch(route('todos.update-status', todo.id), {
            status: newStatus
        }, {
            preserveScroll: true,
            onSuccess: () => {
                // Update local state if needed
            },
            onError: (errors) => {
                console.error('Status change failed:', errors)
            }
        })
    }

    // Handle category change via drag and drop
    const handleCategoryChange = (todo, newCategoryId) => {
        if (todo.category_id === newCategoryId) return

        router.patch(route('todos.update-category', todo.id), {
            category_id: newCategoryId
        }, {
            preserveScroll: true,
            onSuccess: () => {
                // Update local state if needed
            },
            onError: (errors) => {
                console.error('Category change failed:', errors)
            }
        })
    }

    // Drag start handler
    const onDragStart = (item) => {
        isDragging.value = true
        draggedItem.value = item
        console.log('Drag started:', item)
    }

    // Drag end handler
    const onDragEnd = () => {
        setTimeout(() => {
            isDragging.value = false
            draggedItem.value = null
            console.log('Drag ended')
        }, 100)
    }

    // Check if item can be dropped in a specific zone
    const canDrop = (targetType, targetValue, item) => {
        if (!item) return false

        switch (targetType) {
            case 'priority':
                return item.priority !== targetValue
            case 'status':
                return item.status !== targetValue
            case 'category':
                return item.category_id !== targetValue
            default:
                return true
        }
    }

    // Get drag and drop options for vuedraggable
    const getDragOptions = (group = 'todos', viewMode = 'list') => {
        let groupConfig;
        let sortEnabled = true;

        // Configure group settings based on context and view mode
        if (viewMode === 'kanban') {
            // Kanban view - allow dragging between columns
            if (group.startsWith('kanban-')) {
                groupConfig = {
                    name: 'kanban-todos',
                    pull: true,
                    put: true
                };
                sortEnabled = true;
            } else {
                groupConfig = 'kanban-todos';
                sortEnabled = true;
            }
        } else {
            // List view - separate groups for main list and drop zones
            if (group.startsWith('status-')) {
                // List view status drop zones
                groupConfig = {
                    name: 'list-todos',
                    pull: false,
                    put: true
                };
                sortEnabled = false;
            } else if (group === 'todos') {
                // Main todo list in list view
                groupConfig = {
                    name: 'list-todos',
                    pull: 'clone',
                    put: false
                };
                sortEnabled = true;
            } else {
                groupConfig = group;
                sortEnabled = true;
            }
        }

        return {
            group: groupConfig,
            animation: 200,
            ghostClass: 'drag-ghost',
            chosenClass: 'drag-chosen',
            dragClass: 'drag-active',
            forceFallback: false,
            swapThreshold: 0.65,
            emptyInsertThreshold: 50,
            sort: sortEnabled,
        }
    }

    return {
        isDragging,
        draggedItem,
        dropZones,
        handleTodoReorder,
        handlePriorityChange,
        handleStatusChange,
        handleCategoryChange,
        onDragStart,
        onDragEnd,
        canDrop,
        getDragOptions
    }
}

// Composable for category drag and drop
export function useCategoryDragAndDrop() {
    const isDragging = ref(false)
    const draggedCategory = ref(null)

    const handleCategoryReorder = (newOrder) => {
        const categoryIds = newOrder.map(category => category.id)
        
        router.post(route('categories.reorder'), {
            category_ids: categoryIds
        }, {
            preserveScroll: true,
            onError: (errors) => {
                console.error('Category reorder failed:', errors)
            }
        })
    }

    const onDragStart = (category) => {
        isDragging.value = true
        draggedCategory.value = category
    }

    const onDragEnd = () => {
        isDragging.value = false
        draggedCategory.value = null
    }

    const getDragOptions = () => {
        return {
            group: 'categories',
            animation: 200,
            ghostClass: 'drag-ghost',
            chosenClass: 'drag-chosen',
            dragClass: 'drag-active',
            forceFallback: true,
            fallbackClass: 'drag-fallback',
            fallbackOnBody: true,
        }
    }

    return {
        isDragging,
        draggedCategory,
        handleCategoryReorder,
        onDragStart,
        onDragEnd,
        getDragOptions
    }
}
