<?php

namespace App\Http\Controllers;

use App\Models\Todo;
use App\Models\Category;
use App\Events\TodoCreated;
use App\Events\TodoUpdated;
use App\Events\TodoDeleted;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Inertia\Inertia;

class TodoController extends Controller
{
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10);
        $sortBy = $request->get('sort_by', 'order');
        $sortOrder = $request->get('sort_order', 'asc');
        $status = $request->get('status');
        $priority = $request->get('priority');
        $categoryId = $request->get('category_id');
        $search = $request->get('search');

        $query = auth()->user()->todos()->with('category');

        // Apply filters
        if ($status) {
            $query->where('status', $status);
        }

        if ($priority) {
            $query->where('priority', $priority);
        }

        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $query->orderBy($sortBy, $sortOrder);

        $todos = $query->paginate($perPage)->withQueryString();
        $categories = auth()->user()->categories;

        return Inertia::render('Todos/Index', [
            'todos' => $todos,
            'categories' => $categories,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'priority' => $priority,
                'category_id' => $categoryId,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
                'per_page' => $perPage,
            ]
        ]);
    }

    public function create()
    {
        $categories = auth()->user()->categories;
        return Inertia::render('Todos/Create', [
            'categories' => $categories
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,medium,high',
            'due_date' => 'nullable|date',
            'due_time' => 'nullable|date_format:H:i',
            'category_id' => 'nullable|exists:categories,id',
            'is_recurring' => 'boolean',
            'recurring_pattern' => 'nullable|array',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['status'] = 'pending';

        // Normalize time format to H:i:s for database storage
        if (isset($validated['due_time']) && $validated['due_time']) {
            $validated['due_time'] = $this->normalizeTimeFormat($validated['due_time']);
        }

        $todo = Todo::create($validated);

        // Broadcast the todo creation
        broadcast(new TodoCreated($todo));

        return redirect()->route('todos.index')->with('success', 'Todo created successfully!');
    }

    public function show(Todo $todo)
    {
        $this->authorize('view', $todo);
        $todo->load('category');
        return Inertia::render('Todos/Show', [
            'todo' => $todo
        ]);
    }

    public function edit(Todo $todo)
    {
        $this->authorize('update', $todo);
        $categories = auth()->user()->categories;
        $todo->load('category');

        // Format dates for the form
        $todoData = $todo->toArray();
        if ($todo->due_date) {
            $todoData['due_date'] = $todo->due_date->format('Y-m-d');
        }
        if ($todo->due_time) {
            // Ensure time is in H:i format
            try {
                $time = \Carbon\Carbon::createFromFormat('H:i:s', $todo->due_time);
                $todoData['due_time'] = $time->format('H:i');
            } catch (\Exception $e) {
                // If parsing fails, try to extract H:i from the string
                $todoData['due_time'] = substr($todo->due_time, 0, 5);
            }
        }

        return Inertia::render('Todos/Edit', [
            'todo' => $todoData,
            'categories' => $categories
        ]);
    }

    public function update(Request $request, Todo $todo)
    {
        $this->authorize('update', $todo);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'priority' => 'required|in:low,medium,high',
            'status' => 'required|in:pending,completed,archived',
            'due_date' => 'nullable|date',
            'due_time' => 'nullable|date_format:H:i',
            'category_id' => 'nullable|exists:categories,id',
            'is_recurring' => 'boolean',
            'recurring_pattern' => 'nullable|array',
        ]);

        // Normalize time format to H:i:s for database storage
        if (isset($validated['due_time']) && $validated['due_time']) {
            $validated['due_time'] = $this->normalizeTimeFormat($validated['due_time']);
        }

        $todo->update($validated);

        // Broadcast the todo update
        broadcast(new TodoUpdated($todo, 'updated'));

        return redirect()->route('todos.index')->with('success', 'Todo updated successfully!');
    }

    public function destroy(Todo $todo)
    {
        $this->authorize('delete', $todo);

        $todoId = $todo->id;
        $userId = $todo->user_id;

        $todo->delete();

        // Broadcast the todo deletion
        broadcast(new TodoDeleted($todoId, $userId));

        return redirect()->route('todos.index')->with('success', 'Todo deleted successfully!');
    }

    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'todo_ids' => 'required|array',
            'todo_ids.*' => 'exists:todos,id',
            'action' => 'required|in:complete,delete,update_status,update_priority,update_category',
            'status' => 'nullable|in:pending,completed,archived',
            'priority' => 'nullable|in:low,medium,high',
            'category_id' => 'nullable|exists:categories,id',
        ]);

        $todos = auth()->user()->todos()->whereIn('id', $validated['todo_ids'])->get();

        foreach ($todos as $todo) {
            $this->authorize('update', $todo);
        }

        switch ($validated['action']) {
            case 'complete':
                $todos->each->markAsCompleted();
                $message = 'Todos marked as completed successfully!';
                break;
            case 'delete':
                foreach ($todos as $todo) {
                    $this->authorize('delete', $todo);
                }
                $todos->each->delete();
                $message = 'Todos deleted successfully!';
                break;
            case 'update_status':
                $todos->each->update(['status' => $validated['status']]);
                $message = "Todos status updated to {$validated['status']} successfully!";
                break;
            case 'update_priority':
                $todos->each->update(['priority' => $validated['priority']]);
                $message = "Todos priority updated to {$validated['priority']} successfully!";
                break;
            case 'update_category':
                $todos->each->update(['category_id' => $validated['category_id']]);
                $message = 'Todos category updated successfully!';
                break;
        }

        return redirect()->route('todos.index')->with('success', $message);
    }

    public function toggleStatus(Todo $todo)
    {
        $this->authorize('update', $todo);

        $todo->status = $todo->status === 'completed' ? 'pending' : 'completed';
        $todo->save();

        // Broadcast the status toggle
        broadcast(new TodoUpdated($todo, 'status_toggled'));

        return redirect()->back()->with('success', 'Todo status updated successfully!');
    }

    public function reorder(Request $request)
    {
        $validated = $request->validate([
            'todo_ids' => 'required|array',
            'todo_ids.*' => 'exists:todos,id',
        ]);

        $todos = auth()->user()->todos()->whereIn('id', $validated['todo_ids'])->get();

        foreach ($todos as $todo) {
            $this->authorize('update', $todo);
        }

        // Update the order based on the array position
        foreach ($validated['todo_ids'] as $index => $todoId) {
            auth()->user()->todos()->where('id', $todoId)->update(['order' => $index]);
        }

        return redirect()->back()->with('success', 'Todos reordered successfully!');
    }

    public function updatePriority(Request $request, Todo $todo)
    {
        $this->authorize('update', $todo);

        $validated = $request->validate([
            'priority' => 'required|in:low,medium,high',
        ]);

        $todo->update($validated);

        // Broadcast the priority update
        broadcast(new TodoUpdated($todo, 'priority_updated'));

        return redirect()->back()->with('success', 'Todo priority updated successfully!');
    }

    public function updateStatus(Request $request, Todo $todo)
    {
        $this->authorize('update', $todo);

        $validated = $request->validate([
            'status' => 'required|in:pending,completed,archived',
        ]);

        $todo->update($validated);

        // Broadcast the status update
        broadcast(new TodoUpdated($todo, 'status_updated'));

        return redirect()->back()->with('success', 'Todo status updated successfully!');
    }

    public function updateCategory(Request $request, Todo $todo)
    {
        $this->authorize('update', $todo);

        $validated = $request->validate([
            'category_id' => 'nullable|exists:categories,id',
        ]);

        $todo->update($validated);

        // Broadcast the category update
        broadcast(new TodoUpdated($todo, 'category_updated'));

        return redirect()->back()->with('success', 'Todo category updated successfully!');
    }

    /**
     * Normalize time format for database storage
     */
    private function normalizeTimeFormat($time)
    {
        if (!$time) return null;

        // If time is already in H:i:s format, return as is
        if (preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $time)) {
            return $time;
        }

        // If time is in H:i format, add seconds
        if (preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time)) {
            return $time . ':00';
        }

        // Try to parse and format the time
        try {
            $carbonTime = \Carbon\Carbon::createFromFormat('H:i', $time);
            return $carbonTime->format('H:i:s');
        } catch (\Exception $e) {
            // If all else fails, return the original time
            return $time;
        }
    }
}
