<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Todo extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'priority',
        'status',
        'due_date',
        'due_time',
        'category_id',
        'is_recurring',
        'recurring_pattern',
        'google_event_id',
        'order',
    ];

    protected function casts(): array
    {
        return [
            'due_date' => 'date',
            'is_recurring' => 'boolean',
            'recurring_pattern' => 'array',
        ];
    }

    /**
     * Get the user that owns the todo.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that owns the todo.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the reminders for the todo.
     */
    public function reminders()
    {
        return $this->hasMany(Reminder::class);
    }

    /**
     * Scope a query to only include pending todos.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include completed todos.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include archived todos.
     */
    public function scopeArchived($query)
    {
        return $query->where('status', 'archived');
    }

    /**
     * Scope a query to only include overdue todos.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', now()->toDateString());
    }

    /**
     * Scope a query to only include todos for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include todos with high priority.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    /**
     * Check if the todo is overdue.
     */
    public function isOverdue()
    {
        if ($this->status !== 'pending' || !$this->due_date) {
            return false;
        }

        $dueDateTime = $this->due_date;
        if ($this->due_time) {
            $dueDateTime = Carbon::parse($this->due_date->format('Y-m-d') . ' ' . $this->due_time);
        }

        return $dueDateTime->isPast();
    }

    /**
     * Mark the todo as completed.
     */
    public function markAsCompleted()
    {
        $this->update(['status' => 'completed']);
    }

    /**
     * Mark the todo as pending.
     */
    public function markAsPending()
    {
        $this->update(['status' => 'pending']);
    }

    /**
     * Mark the todo as archived.
     */
    public function markAsArchived()
    {
        $this->update(['status' => 'archived']);
    }
}
