<script setup>
import { ref, computed, watch } from 'vue'
import { Transition } from 'vue'

const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    message: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: 'info',
        validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    duration: {
        type: Number,
        default: 3000
    },
    position: {
        type: String,
        default: 'top-right',
        validator: (value) => ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center'].includes(value)
    }
})

const emit = defineEmits(['close'])

const isVisible = ref(props.show)

// Watch for show prop changes
watch(() => props.show, (newValue) => {
    isVisible.value = newValue
    
    if (newValue && props.duration > 0) {
        setTimeout(() => {
            close()
        }, props.duration)
    }
})

// Close notification
const close = () => {
    isVisible.value = false
    emit('close')
}

// Computed classes for different types
const typeClasses = computed(() => {
    switch (props.type) {
        case 'success':
            return 'bg-green-50 border-green-200 text-green-800'
        case 'error':
            return 'bg-red-50 border-red-200 text-red-800'
        case 'warning':
            return 'bg-yellow-50 border-yellow-200 text-yellow-800'
        case 'info':
        default:
            return 'bg-blue-50 border-blue-200 text-blue-800'
    }
})

// Computed classes for position
const positionClasses = computed(() => {
    switch (props.position) {
        case 'top-left':
            return 'top-4 left-4'
        case 'bottom-right':
            return 'bottom-4 right-4'
        case 'bottom-left':
            return 'bottom-4 left-4'
        case 'top-center':
            return 'top-4 left-1/2 transform -translate-x-1/2'
        case 'top-right':
        default:
            return 'top-4 right-4'
    }
})

// Icon for different types
const getIcon = () => {
    switch (props.type) {
        case 'success':
            return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
        case 'error':
            return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
        case 'warning':
            return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z'
        case 'info':
        default:
            return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
    }
}
</script>

<template>
    <Teleport to="body">
        <Transition
            enter-active-class="transition duration-300 ease-out"
            enter-from-class="transform translate-x-full opacity-0"
            enter-to-class="transform translate-x-0 opacity-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="transform translate-x-0 opacity-100"
            leave-to-class="transform translate-x-full opacity-0"
        >
            <div
                v-if="isVisible"
                :class="[
                    'fixed z-50 max-w-sm w-full shadow-lg rounded-lg border pointer-events-auto',
                    typeClasses,
                    positionClasses
                ]"
                role="alert"
            >
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg 
                                class="h-5 w-5" 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24"
                            >
                                <path 
                                    stroke-linecap="round" 
                                    stroke-linejoin="round" 
                                    stroke-width="2" 
                                    :d="getIcon()"
                                />
                            </svg>
                        </div>
                        <div class="ml-3 w-0 flex-1">
                            <p class="text-sm font-medium">
                                {{ message }}
                            </p>
                        </div>
                        <div class="ml-4 flex-shrink-0 flex">
                            <button
                                @click="close"
                                class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150"
                            >
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </Transition>
    </Teleport>
</template>

<style scoped>
/* Additional styles for better visual feedback */
.pointer-events-auto {
    pointer-events: auto;
}
</style>
