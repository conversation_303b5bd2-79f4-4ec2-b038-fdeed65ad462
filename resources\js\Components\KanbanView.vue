<script setup>
import { ref, computed, watch } from 'vue'
import { Link } from '@inertiajs/vue3'
import draggable from 'vuedraggable'
import { useTodoDragAndDrop } from '@/composables/useDragAndDrop'

const props = defineProps({
    todos: Array,
    categories: Array,
    selectedTodos: Array,
})

const emit = defineEmits(['toggle-select', 'toggle-status', 'delete-todo'])

const {
    isDragging,
    handleStatusChange,
    onDragStart,
    onDragEnd,
    getDragOptions
} = useTodoDragAndDrop()

// Group todos by status
const todosByStatus = computed(() => {
    const grouped = {
        pending: [],
        completed: [],
        cancelled: []
    }
    
    props.todos.forEach(todo => {
        if (grouped[todo.status]) {
            grouped[todo.status].push(todo)
        }
    })
    
    return grouped
})

// Handle status change via drag and drop
const onStatusChange = (evt, status) => {
    if (evt.added) {
        // A todo was added to this column
        const movedTodo = evt.added.element
        if (movedTodo && movedTodo.status !== status) {
            handleStatusChange(movedTodo, status)
        }
    }
}

// Priority colors
const getPriorityColor = (priority) => {
    switch (priority) {
        case 'high': return 'text-red-600 bg-red-50 border-red-200';
        case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        case 'low': return 'text-green-600 bg-green-50 border-green-200';
        default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
};

// Format date
const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
    });
};

// Status configurations
const statusConfig = {
    pending: {
        title: 'Pending',
        color: 'bg-blue-50 border-blue-200',
        headerColor: 'bg-blue-100 text-blue-800',
        icon: '⏳'
    },
    completed: {
        title: 'Completed',
        color: 'bg-green-50 border-green-200',
        headerColor: 'bg-green-100 text-green-800',
        icon: '✅'
    },
    cancelled: {
        title: 'Cancelled',
        color: 'bg-gray-50 border-gray-200',
        headerColor: 'bg-gray-100 text-gray-800',
        icon: '❌'
    }
}
</script>

<template>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div 
            v-for="(status, statusKey) in statusConfig" 
            :key="statusKey"
            class="flex flex-col"
        >
            <!-- Column Header -->
            <div :class="['p-4 rounded-t-lg border-b-2', status.headerColor]">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-lg">{{ status.icon }}</span>
                        <h3 class="font-semibold">{{ status.title }}</h3>
                    </div>
                    <span class="text-sm font-medium bg-white px-2 py-1 rounded-full">
                        {{ todosByStatus[statusKey].length }}
                    </span>
                </div>
            </div>

            <!-- Droppable Column -->
            <div :class="['flex-1 p-4 min-h-96 border-l border-r border-b rounded-b-lg', status.color]">
                <draggable
                    v-model="todosByStatus[statusKey]"
                    v-bind="getDragOptions(`kanban-${statusKey}`)"
                    @start="onDragStart"
                    @end="onDragEnd"
                    @change="(evt) => onStatusChange(evt, statusKey)"
                    item-key="id"
                    class="space-y-3 min-h-full"
                    :empty-insert-threshold="50"
                >
                    <template #item="{ element: todo }">
                        <div 
                            class="bg-white rounded-lg border border-border p-4 shadow-sm hover:shadow-md transition-all duration-200 cursor-move"
                            :class="{ 
                                'opacity-50': isDragging,
                                'transform rotate-2': isDragging && todo.id === draggedItem?.id
                            }"
                        >
                            <!-- Todo Header -->
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center space-x-2 flex-1">
                                    <!-- Selection Checkbox -->
                                    <label class="flex-shrink-0 cursor-pointer">
                                        <input
                                            type="checkbox"
                                            :value="todo.id"
                                            :checked="selectedTodos.includes(todo.id)"
                                            @change="emit('toggle-select', todo.id)"
                                            class="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                                        />
                                    </label>
                                    
                                    <!-- Priority Badge -->
                                    <span :class="getPriorityColor(todo.priority)" class="px-2 py-1 text-xs font-medium rounded-full border">
                                        {{ todo.priority }}
                                    </span>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-1">
                                    <Link :href="route('todos.show', todo.id)" class="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50 transition-colors">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </Link>
                                    <Link :href="route('todos.edit', todo.id)" class="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-50 transition-colors">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </Link>
                                </div>
                            </div>

                            <!-- Todo Content -->
                            <div>
                                <h4 :class="['font-semibold text-sm mb-1', todo.status === 'completed' ? 'line-through text-muted-foreground' : 'text-foreground']">
                                    {{ todo.title }}
                                </h4>
                                <p v-if="todo.description" :class="['text-xs text-muted-foreground mb-2', todo.status === 'completed' ? 'line-through' : '']">
                                    {{ todo.description.length > 60 ? todo.description.substring(0, 60) + '...' : todo.description }}
                                </p>
                            </div>

                            <!-- Todo Footer -->
                            <div class="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                                <div class="flex items-center space-x-2">
                                    <span v-if="todo.category" class="px-2 py-1 text-xs font-medium rounded-full bg-accent/50 text-accent-foreground border border-accent">
                                        {{ todo.category.name }}
                                    </span>
                                </div>
                                <div class="flex items-center space-x-2 text-xs text-muted-foreground">
                                    <span v-if="todo.due_date" class="flex items-center space-x-1">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>{{ formatDate(todo.due_date) }}</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </template>
                    
                    <!-- Empty State -->
                    <template #footer>
                        <div v-if="todosByStatus[statusKey].length === 0" class="text-center py-8 text-muted-foreground">
                            <div class="text-4xl mb-2">{{ status.icon }}</div>
                            <p class="text-sm">No {{ status.title.toLowerCase() }} todos</p>
                            <p class="text-xs mt-1">Drag todos here to change status</p>
                        </div>
                    </template>
                </draggable>
            </div>
        </div>
    </div>
</template>

<style scoped>
.drag-ghost {
    opacity: 0.5;
    background: #f3f4f6;
    transform: rotate(5deg);
}

.drag-chosen {
    background: #f0f9ff;
    border: 2px dashed #3b82f6;
}

.sortable-drag {
    opacity: 0.8;
    transform: rotate(5deg);
}

.sortable-ghost {
    opacity: 0.4;
    background: #e5e7eb;
    border: 2px dashed #9ca3af;
}
</style>
