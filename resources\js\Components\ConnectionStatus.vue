<script setup>
import { computed } from 'vue'

const props = defineProps({
    isConnected: {
        type: Boolean,
        default: false
    },
    lastUpdate: {
        type: String,
        default: null
    },
    showDetails: {
        type: Boolean,
        default: false
    }
})

// Format last update time
const formattedLastUpdate = computed(() => {
    if (!props.lastUpdate) return 'Never'
    
    try {
        const date = new Date(props.lastUpdate)
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    } catch (error) {
        return 'Invalid date'
    }
})

// Connection status text
const statusText = computed(() => {
    return props.isConnected ? 'Connected' : 'Disconnected'
})

// Connection status classes
const statusClasses = computed(() => {
    return props.isConnected 
        ? 'text-green-600 bg-green-50 border-green-200' 
        : 'text-red-600 bg-red-50 border-red-200'
})

// Pulse animation for connected state
const pulseClasses = computed(() => {
    return props.isConnected ? 'animate-pulse' : ''
})
</script>

<template>
    <div class="flex items-center space-x-2">
        <!-- Connection indicator dot -->
        <div class="relative">
            <div 
                :class="[
                    'w-3 h-3 rounded-full',
                    props.isConnected ? 'bg-green-500' : 'bg-red-500',
                    pulseClasses
                ]"
            ></div>
            <!-- Ping animation for connected state -->
            <div 
                v-if="isConnected"
                class="absolute top-0 left-0 w-3 h-3 bg-green-500 rounded-full animate-ping opacity-75"
            ></div>
        </div>

        <!-- Status text -->
        <span 
            :class="[
                'text-xs font-medium px-2 py-1 rounded-full border',
                statusClasses
            ]"
        >
            {{ statusText }}
        </span>

        <!-- Detailed info (optional) -->
        <div v-if="showDetails" class="text-xs text-muted-foreground">
            <span>Last update: {{ formattedLastUpdate }}</span>
        </div>
    </div>
</template>

<style scoped>
@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes pulse {
    50% {
        opacity: .5;
    }
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
